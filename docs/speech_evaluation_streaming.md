# 语音评测流式结果功能说明

## 概述

SpeechEvaluation类现在支持流式结果返回，可以在评测过程中实时获取中间结果，而不仅仅是等待最终结果。这对于需要实时反馈的应用场景非常有用。

## 新增功能

### 1. 流式结果通知器

- `intermediateResultNotifier`: 中间结果通知器 (eof = 0)
- `finalResultNotifier`: 最终结果通知器 (eof = 1)

### 2. 流式评测方法

- `startWordWithRealtime(String refText)`: 启动流式单词评测
- `startSentenceWithRealtime(String refText)`: 启动流式句子评测  
- `startParaWithRealtime(String refText)`: 启动流式段落评测

### 3. 监听器管理方法

- `addIntermediateResultListener(VoidCallback listener)`: 添加中间结果监听器
- `removeIntermediateResultListener(VoidCallback listener)`: 移除中间结果监听器
- `addFinalResultListener(VoidCallback listener)`: 添加最终结果监听器
- `removeFinalResultListener(VoidCallback listener)`: 移除最终结果监听器

### 4. 配置方法

- `setRealtimeFeedback(bool enabled)`: 手动启用/禁用流式反馈
- `isRealtimeFeedbackEnabled`: 检查是否启用了流式反馈

## 使用方法

### 基本使用

```dart
import 'package:lsenglish/utils/speech_evaluation.dart';

class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  final SpeechEvaluation _speechEval = SpeechEvaluation.instance;
  
  @override
  void initState() {
    super.initState();
    _initSpeechEvaluation();
  }
  
  void _initSpeechEvaluation() async {
    // 初始化引擎
    await _speechEval.init();
    
    // 监听中间结果
    _speechEval.addIntermediateResultListener(_onIntermediateResult);
    
    // 监听最终结果
    _speechEval.addFinalResultListener(_onFinalResult);
  }
  
  void _onIntermediateResult() {
    final result = _speechEval.intermediateResultNotifier.value;
    if (result != null) {
      print("收到中间结果: eof=${result.eof}");
      // 处理中间结果...
    }
  }
  
  void _onFinalResult() {
    final result = _speechEval.finalResultNotifier.value;
    if (result != null) {
      print("收到最终结果: eof=${result.eof}");
      // 处理最终结果...
    }
  }
  
  void _startStreamingEvaluation() {
    // 启动流式评测
    _speechEval.startSentenceWithRealtime("Hello world");
  }
  
  @override
  void dispose() {
    // 移除监听器
    _speechEval.removeIntermediateResultListener(_onIntermediateResult);
    _speechEval.removeFinalResultListener(_onFinalResult);
    super.dispose();
  }
}
```

### 高级使用

```dart
// 手动控制流式反馈
_speechEval.setRealtimeFeedback(true);
_speechEval.startSentence("Hello world"); // 这样也会启用流式反馈

// 检查是否启用了流式反馈
if (_speechEval.isRealtimeFeedbackEnabled) {
  print("流式反馈已启用");
}
```

## 结果字段说明

### eof字段含义

- `eof = 0`: 中间结果，评测还在进行中
- `eof = 1`: 最终结果，评测已完成

### 中间结果特点

1. **实时性**: 在录音过程中就能收到部分评测结果
2. **增量性**: 每次中间结果可能包含更多的评测信息
3. **不完整性**: 中间结果可能不包含完整的评测数据

### 最终结果特点

1. **完整性**: 包含完整的评测结果
2. **准确性**: 基于完整音频的最终评测分数
3. **终结性**: 收到最终结果后，评测流程结束

## 配置参数

当启用流式反馈时，会自动在请求参数中添加：

```json
{
  "refText": "参考文本",
  "coreType": "sent.eval.pro",
  "realtime_feedback": "1"
}
```

## 注意事项

1. **向后兼容**: 原有的`resultNotifier`仍然可用，会同时接收中间结果和最终结果
2. **内存管理**: 记得在组件销毁时移除监听器，避免内存泄漏
3. **错误处理**: 流式评测中的错误仍然通过`errorNotifier`通知
4. **状态管理**: `isRecording`状态在收到最终结果后才会变为false

## 示例代码

完整的使用示例请参考：`lib/examples/speech_evaluation_streaming_example.dart`

## 技术实现

1. **参数配置**: 通过在KYRecordSetting.request中添加`realtime_feedback: "1"`启用
2. **结果解析**: 根据返回JSON中的`eof`字段判断结果类型
3. **状态管理**: 使用独立的ValueNotifier管理中间结果和最终结果
4. **生命周期**: 自动在收到最终结果后重置流式配置状态
