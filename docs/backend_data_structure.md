# 后端数据结构设计

## 数据库表结构设计

### 1. 学习会话表 (learning_sessions)
```sql
CREATE TABLE learning_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    resource_id VARCHAR(100) NOT NULL,
    resource_type TINYINT NOT NULL DEFAULT 2,
    session_start_time BIGINT NOT NULL,
    session_end_time BIGINT NOT NULL,
    total_duration INT NOT NULL,
    ls_mode_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    ls_times INT DEFAULT 0,
    device_info JSON,
    app_version VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_resource (user_id, resource_id),
    INDEX idx_session_time (session_start_time),
    INDEX idx_created_at (created_at)
);
```

### 2. 句子学习记录表 (sentence_learning_records)
```sql
CREATE TABLE sentence_learning_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    resource_id VARCHAR(100) NOT NULL,
    sentence_id VARCHAR(50) NOT NULL, -- subtitle_start_subtitle_end
    subtitle_start_time INT NOT NULL,
    subtitle_end_time INT NOT NULL,
    subtitle_duration INT NOT NULL,
    
    -- 播放统计
    play_count INT DEFAULT 0,
    play_total_duration INT DEFAULT 0,
    play_avg_duration DECIMAL(8,2) DEFAULT 0,
    
    -- 录音统计
    record_count INT DEFAULT 0,
    record_total_duration INT DEFAULT 0,
    record_avg_duration DECIMAL(8,2) DEFAULT 0,
    
    -- 学习效果指标
    mastery_score DECIMAL(3,2) DEFAULT 0, -- 掌握度评分 0-1
    difficulty_level TINYINT DEFAULT 0,   -- 难度等级 1-5
    last_interaction_time BIGINT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES learning_sessions(id),
    UNIQUE KEY uk_session_sentence (session_id, sentence_id),
    INDEX idx_user_sentence (user_id, sentence_id),
    INDEX idx_resource_sentence (resource_id, sentence_id),
    INDEX idx_mastery_score (mastery_score)
);
```

### 3. 学习行为事件表 (learning_behavior_events)
```sql
CREATE TABLE learning_behavior_events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    resource_id VARCHAR(100) NOT NULL,
    sentence_id VARCHAR(50),
    
    event_type ENUM('play_start', 'play_end', 'record_start', 'record_end', 'pause', 'resume') NOT NULL,
    event_time BIGINT NOT NULL,
    duration INT DEFAULT 0,
    ls_mode BOOLEAN NOT NULL,
    
    -- 上下文信息
    subtitle_index INT,
    subtitle_start_time INT,
    subtitle_end_time INT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES learning_sessions(id),
    INDEX idx_session_event (session_id, event_type),
    INDEX idx_user_time (user_id, event_time),
    INDEX idx_event_type_time (event_type, event_time)
);
```

### 4. 学习时段分析表 (learning_time_patterns)
```sql
CREATE TABLE learning_time_patterns (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    date DATE NOT NULL,
    hour_of_day TINYINT NOT NULL, -- 0-23
    day_of_week TINYINT NOT NULL, -- 1-7
    
    session_count INT DEFAULT 0,
    total_duration INT DEFAULT 0,
    avg_session_duration DECIMAL(8,2) DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_date_hour (user_id, date, hour_of_day),
    INDEX idx_user_patterns (user_id, day_of_week, hour_of_day)
);
```

### 5. 内容难度分析表 (content_difficulty_analysis)
```sql
CREATE TABLE content_difficulty_analysis (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    resource_id VARCHAR(100) NOT NULL,
    sentence_id VARCHAR(50) NOT NULL,
    
    -- 统计指标
    total_users INT DEFAULT 0,
    avg_play_count DECIMAL(6,2) DEFAULT 0,
    avg_record_count DECIMAL(6,2) DEFAULT 0,
    avg_mastery_score DECIMAL(3,2) DEFAULT 0,
    
    -- 难度评估
    difficulty_score DECIMAL(3,2) DEFAULT 0, -- 0-1，越高越难
    popularity_score DECIMAL(3,2) DEFAULT 0, -- 0-1，越高越受欢迎
    
    last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_resource_sentence (resource_id, sentence_id),
    INDEX idx_difficulty (difficulty_score),
    INDEX idx_popularity (popularity_score)
);
```

## API 数据格式设计

### 上传学习数据 API

**POST /api/v2/learning/sessions**

```json
{
  "sessionData": {
    "resourceId": "resource_123",
    "resourceType": 2,
    "sessionStartTime": 1640995200,
    "sessionEndTime": 1640998800,
    "totalDuration": 3600,
    "lsModeEnabled": true,
    "lsTimes": 1,
    "deviceInfo": {
      "platform": "iOS",
      "version": "15.0",
      "model": "iPhone 13"
    },
    "appVersion": "2.1.0"
  },
  "sentenceRecords": [
    {
      "sentenceId": "1000_3000",
      "subtitleStartTime": 1000,
      "subtitleEndTime": 3000,
      "subtitleDuration": 2000,
      "playCount": 5,
      "playTotalDuration": 8500,
      "recordCount": 3,
      "recordTotalDuration": 5200,
      "lastInteractionTime": 1640997000
    }
  ],
  "behaviorEvents": [
    {
      "eventType": "play_start",
      "eventTime": 1640995300,
      "sentenceId": "1000_3000",
      "lsMode": true,
      "subtitleIndex": 0,
      "subtitleStartTime": 1000,
      "subtitleEndTime": 3000
    },
    {
      "eventType": "play_end",
      "eventTime": 1640995302,
      "duration": 2000,
      "sentenceId": "1000_3000",
      "lsMode": true
    }
  ]
}
```

### 响应格式

```json
{
  "success": true,
  "data": {
    "sessionId": 12345,
    "processedRecords": 15,
    "processedEvents": 30,
    "analysisResults": {
      "masteryScore": 0.75,
      "recommendedSentences": ["2000_4000", "5000_7000"],
      "learningInsights": {
        "strongPoints": ["pronunciation", "rhythm"],
        "improvementAreas": ["intonation"],
        "nextSteps": "Focus on longer sentences"
      }
    }
  },
  "timestamp": 1640998800
}
```

## 数据分析能力

### 1. 实时分析
- 学习进度跟踪
- 掌握度评估
- 个性化推荐

### 2. 趋势分析
- 学习习惯模式
- 进步趋势
- 内容偏好

### 3. 智能洞察
- 学习效率分析
- 难点识别
- 学习路径优化

### 4. 内容优化
- 内容难度评估
- 热门内容识别
- 课程优化建议

## 性能优化策略

### 1. 数据分区
- 按时间分区存储历史数据
- 按用户分区提高查询性能

### 2. 索引优化
- 复合索引优化查询
- 覆盖索引减少回表

### 3. 缓存策略
- Redis缓存热点数据
- 分析结果缓存

### 4. 异步处理
- 消息队列处理大批量数据
- 后台任务计算分析结果

## 数据隐私和安全

### 1. 数据脱敏
- 敏感信息加密存储
- 分析时数据匿名化

### 2. 访问控制
- 基于角色的权限控制
- API访问限流

### 3. 数据备份
- 定期数据备份
- 灾难恢复方案

这个设计既能满足当前的统计需求，又为未来的数据分析和AI应用奠定了基础。
