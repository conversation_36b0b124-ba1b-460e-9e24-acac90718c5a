import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/size_extension.dart';
import '../controllers/resourcelib_controller.dart';

class ResourcelibView extends GetView<ResourcelibController> {
  const ResourcelibView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.whs),
              child: Text(
                '资料库',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 34.whs,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Gap(16.whs),
            Padding(padding: EdgeInsets.only(left: 16.whs), child: Text("精选推荐", style: TextStyle(fontSize: 24.whs, fontWeight: FontWeight.w600))),
            Gap(16.whs),
            SizedBox(
              height: 163.whs,
              child: Obx(() => ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: controller.recommendList.length,
                    itemBuilder: (BuildContext context, int index) {
                      return GestureDetector(
                        onTap: () => controller.onRecommendClick(index),
                        child: Padding(
                          padding: EdgeInsets.only(left: index == 0 ? 16.whs : 8.whs, right: 8.whs),
                          child: AspectRatio(
                            aspectRatio: 330 / 160,
                            child: Stack(
                              children: [
                                Positioned.fill(
                                    child: Container(
                                        clipBehavior: Clip.antiAlias,
                                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(16.whs)),
                                        child: ImageLoader(controller.recommendList[index].cover ?? "")))
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  )),
            ),
            Gap(16.whs),
            Obx(
              () => ListView.builder(
                shrinkWrap: true,
                scrollDirection: Axis.vertical,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: controller.categoryTypeList.length,
                itemBuilder: (BuildContext context, int index) {
                  return Theme(
                    data: Theme.of(context).copyWith(tabBarTheme: const TabBarTheme(labelPadding: EdgeInsets.zero)),
                    child: TabBar(
                      tabAlignment: TabAlignment.start,
                      labelPadding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 4.whs),
                      padding: EdgeInsets.symmetric(vertical: 8.whs),
                      isScrollable: true,
                      controller: controller.tabControllers[index],
                      overlayColor: WidgetStateProperty.all(Colors.transparent),
                      tabs: controller.categoryTypeList[index].categories!.map((label) => Text(label.name ?? "")).toList(),
                      labelColor: Colors.black,
                      labelStyle: TextStyle(fontSize: 16.whs, fontWeight: FontWeight.w600),
                      indicatorColor: Colors.black,
                      unselectedLabelStyle: TextStyle(fontSize: 16.whs, fontWeight: FontWeight.w500),
                      unselectedLabelColor: const Color(0xFF625B71),
                      dividerColor: Colors.transparent,
                      indicatorSize: TabBarIndicatorSize.label,
                    ),
                  );
                },
              ),
            ),
            Gap(20.whs),
            Obx(() => ListView.builder(
                  scrollDirection: Axis.vertical,
                  itemCount: controller.resourceList.length,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (BuildContext context, int index) {
                    return GestureDetector(
                      onTap: () {
                        controller.onItemClick(index);
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Padding(
                          padding: EdgeInsets.only(top: index == 0 ? 0 : 8.whs, bottom: 8.whs, left: 16.whs, right: 16.whs),
                          child: SizedBox(
                            height: 100.whs,
                            child: Row(
                              children: [
                                Container(
                                    clipBehavior: Clip.antiAlias,
                                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(16.whs)),
                                    child: AspectRatio(
                                      aspectRatio: 1,
                                      child: ImageLoader(controller.resourceList[index].cover ?? "", size: 100.whs),
                                    )),
                                Gap(8.whs),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        controller.resourceList[index].name ?? "--",
                                        style: TextStyle(fontSize: 16.whs, fontWeight: FontWeight.w600),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      Gap(8.whs),
                                      Text(controller.resourceList[index].contentType == 1 ? "剧集" : "视频"),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ))
          ],
        ),
      ),
    );
  }
}
