import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/lang.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:lsenglish/widgets/language.dart';

class SettingController extends GetxController {
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void toggleTheme(bool isDarkMode) {
    Config().themeMode.value = isDarkMode ? ThemeMode.dark : ThemeMode.light;
    Get.changeThemeMode(Config().themeMode.value); // 更新全局主题
    GetStorage().write('isDarkMode', isDarkMode);
  }

  void uploadLog() async {
    await Logger().uploadLogFile();
    toastInfo("上传成功");
  }

  void switchLanguage() {
    Get.bottomSheet(
      LanguageChooseWidget(onLanguageSelected: (lang) async {
        Get.back();
        await Future.delayed(const Duration(milliseconds: 300));
        final parts = lang.code!.split('-');
        Locale locale;
        if (parts.length == 2) {
          locale = Locale(parts[0], parts[1]);
        } else {
          locale = Locale(lang.code!);
        }
        Get.updateLocale(locale);
        saveLang(lang.code!);
      }),
      enterBottomSheetDuration: const Duration(milliseconds: 300), // 添加打开动画时长
      exitBottomSheetDuration: const Duration(milliseconds: 300), // 添加关闭动画时长
    );
  }
}
