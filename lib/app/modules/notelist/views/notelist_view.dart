import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/widgets/default_widget.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/widgets/keep_alive.dart';
import 'package:lsenglish/widgets/slanted_indicator.dart';
import 'package:lsenglish/widgets/split_english.dart';

import '../controllers/notelist_controller.dart';

class NotelistView extends GetView<NoteListController> {
  const NotelistView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        surfaceTintColor: Colors.white,
        title: Theme(
          data: Theme.of(context).copyWith(tabBarTheme: const TabBarTheme(labelPadding: EdgeInsets.zero)),
          child: TabBar(
            isScrollable: true,
            padding: EdgeInsets.zero,
            tabAlignment: TabAlignment.startOffset,
            controller: controller.tabController,
            overlayColor: WidgetStateProperty.all(Colors.transparent),
            tabs: ['笔记', '全部']
                .map((label) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 11.0, vertical: 6.0),
                      child: Text(label, style: Get.textTheme.titleMedium),
                    ))
                .toList(),
            labelColor: Colors.black,
            unselectedLabelColor: const Color(0xff999999),
            dividerColor: Colors.transparent,
            indicatorSize: TabBarIndicatorSize.tab,
            indicator: SlantedIndicator(height: 10.whs, radius: 20.whs, color: Get.theme.primaryColor),
          ),
        ),
        centerTitle: true,
      ),
      body: PageView(
        onPageChanged: (index) {
          controller.onPageChanged(index);
        },
        controller: controller.pageController,
        children: [
          KeepAlivePage(
            child: Obx(() => controller.noteSubtitles.isEmpty
                ? const NodataWidget(message: "You don't have note")
                : ListView.builder(
                    itemCount: controller.noteSubtitles.length,
                    itemBuilder: (context, index) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Visibility(
                              visible: index == 0,
                              child: Obx(() => Padding(
                                    padding: EdgeInsets.all(16.whs),
                                    child: Text("共 ${controller.noteSubtitles.length} 条笔记",
                                        style: Get.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
                                  ))),
                          Padding(
                            padding: EdgeInsets.only(
                                top: index == 0 ? 16.whs : 8.whs,
                                bottom: index == controller.noteSubtitles.length - 1 ? 16.whs : 8.whs,
                                left: 16.whs,
                                right: 16.whs),
                            child: GestureDetector(
                              onTap: () => controller.noteClick(index),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 8.whs, vertical: 2.whs),
                                        decoration: ShapeDecoration(
                                          shape: RoundedRectangleBorder(
                                            side: BorderSide(width: 1, color: Get.theme.colorScheme.secondary.withOpacity(0.5)),
                                            borderRadius: BorderRadius.circular(12.whs),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text((controller.noteSubtitles[index].subtitleIndex + 1).toString(),
                                                style: Get.textTheme.titleSmall
                                                    ?.copyWith(color: Get.theme.colorScheme.secondary, fontWeight: FontWeight.bold)),
                                            Text("/",
                                                style: Get.textTheme.titleSmall?.copyWith(color: Get.theme.colorScheme.secondary.withOpacity(0.5))),
                                            Text(controller.subtitles.length.toString(),
                                                style: Get.textTheme.titleSmall
                                                    ?.copyWith(color: Get.theme.colorScheme.secondary.withOpacity(0.5), fontWeight: FontWeight.bold)),
                                          ],
                                        ),
                                      ),
                                      GestureDetector(
                                          onTap: () {
                                            if (controller.localSentenceCollectIndexs.contains(controller.noteSubtitles[index].subtitleIndex)) {
                                              controller.removeOneLocalSentenceCollect(controller.noteSubtitles[index].subtitleIndex);
                                            } else {
                                              controller.addOneLocalSentenceCollect(controller.noteSubtitles[index].subtitleIndex);
                                            }
                                          },
                                          child: Padding(
                                            padding: EdgeInsets.only(left: 16.whs, right: 8.whs, top: 8.whs, bottom: 8.whs),
                                            child: Obx(() => ImageLoader(
                                                  R.heart_select,
                                                  size: 18.whs,
                                                  color: controller.localSentenceCollectIndexs.contains(controller.noteSubtitles[index].subtitleIndex)
                                                      ? Get.theme.primaryColor
                                                      : Get.theme.colorScheme.secondary,
                                                )),
                                          )),
                                      GestureDetector(
                                          onTap: () {},
                                          child: Padding(
                                            padding: EdgeInsets.only(left: 16.whs, right: 8.whs, top: 8.whs, bottom: 8.whs),
                                            child: ImageLoader(
                                              R.edit,
                                              size: 18.whs,
                                              color: Get.theme.colorScheme.secondary,
                                            ),
                                          )),
                                      GestureDetector(
                                          onTap: () {
                                            controller.deleteNote(index);
                                          },
                                          child: Padding(
                                            padding: EdgeInsets.only(left: 16.whs, right: 8.whs, top: 8.whs, bottom: 8.whs),
                                            child: ImageLoader(
                                              R.delete,
                                              size: 18.whs,
                                              color: Get.theme.colorScheme.secondary,
                                            ),
                                          )),
                                    ],
                                  ),
                                  Gap(16.whs),
                                  Obx(
                                    () => SplitEnglishWidget(
                                      subtitle: controller.noteSubtitles[index],
                                      targetLanguageStyle: TextStyle(color: Colors.black, fontSize: 16.whs, fontWeight: FontWeight.bold),
                                      nativeLanguageStyle: TextStyle(color: Get.theme.primaryColor, fontSize: 16.whs, fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                  Gap(16.whs),
                                  GestureDetector(
                                    child: Container(
                                      clipBehavior: Clip.antiAlias,
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFF6F6F6),
                                        borderRadius: BorderRadius.circular(4.whs),
                                      ),
                                      child: IntrinsicHeight(
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: 4,
                                              color: Get.theme.primaryColor,
                                            ),
                                            Gap(8.whs),
                                            Flexible(
                                              child: Padding(
                                                padding: EdgeInsets.all(6.whs),
                                                child: Obx(() => Text(
                                                      controller.noteList[index].content ?? "",
                                                      style: TextStyle(fontSize: 16.whs),
                                                    )),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const Divider(),
                        ],
                      );
                    },
                  )),
          ),
          KeepAlivePage(
              child: Obx(
            () => controller.noteSubtitles.isEmpty
                ? const NodataWidget(message: "You don't have note")
                : ListView.builder(
                    itemCount: controller.subtitles.length,
                    itemBuilder: (context, index) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                top: index == 0 ? 16.whs : 8.whs,
                                bottom: index == controller.noteSubtitles.length - 1 ? 16.whs : 8.whs,
                                left: 16.whs,
                                right: 16.whs),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: 8.whs, vertical: 2.whs),
                                      decoration: ShapeDecoration(
                                        shape: RoundedRectangleBorder(
                                          side: BorderSide(width: 1, color: Get.theme.colorScheme.secondary.withOpacity(0.5)),
                                          borderRadius: BorderRadius.circular(12.whs),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text((controller.subtitles[index].subtitleIndex + 1).toString(),
                                              style: Get.textTheme.titleSmall
                                                  ?.copyWith(color: Get.theme.colorScheme.secondary, fontWeight: FontWeight.bold)),
                                          Text("/",
                                              style: Get.textTheme.titleSmall?.copyWith(color: Get.theme.colorScheme.secondary.withOpacity(0.5))),
                                          Text(controller.subtitles.length.toString(),
                                              style: Get.textTheme.titleSmall
                                                  ?.copyWith(color: Get.theme.colorScheme.secondary.withOpacity(0.5), fontWeight: FontWeight.bold)),
                                        ],
                                      ),
                                    ),
                                    GestureDetector(
                                        onTap: () {
                                          if (controller.localSentenceCollectIndexs.contains(controller.subtitles[index].subtitleIndex)) {
                                            controller.removeOneLocalSentenceCollect(controller.subtitles[index].subtitleIndex);
                                          } else {
                                            controller.addOneLocalSentenceCollect(controller.subtitles[index].subtitleIndex);
                                          }
                                        },
                                        child: Padding(
                                          padding: EdgeInsets.only(left: 16.whs, right: 8.whs, top: 8.whs, bottom: 8.whs),
                                          child: Obx(() => ImageLoader(
                                                R.heart_select,
                                                size: 18.whs,
                                                color: controller.localSentenceCollectIndexs.contains(controller.subtitles[index].subtitleIndex)
                                                    ? Get.theme.primaryColor
                                                    : Get.theme.colorScheme.secondary,
                                              )),
                                        )),
                                    GestureDetector(
                                        onTap: () {},
                                        child: Padding(
                                          padding: EdgeInsets.only(left: 16.whs, right: 8.whs, top: 8.whs, bottom: 8.whs),
                                          child: ImageLoader(
                                            R.edit,
                                            size: 18.whs,
                                            color: Get.theme.colorScheme.secondary,
                                          ),
                                        )),
                                  ],
                                ),
                                Gap(16.whs),
                                Obx(
                                  () => SplitEnglishWidget(
                                    subtitle: controller.subtitles[index],
                                    targetLanguageStyle: TextStyle(color: Colors.black, fontSize: 16.whs, fontWeight: FontWeight.bold),
                                    nativeLanguageStyle: TextStyle(color: Get.theme.primaryColor, fontSize: 16.whs, fontWeight: FontWeight.bold),
                                  ),
                                ),
                                Visibility(
                                  visible: (controller.notesMap[controller.subtitles[index].subtitleIndex]?.content ?? "") != "",
                                  child: GestureDetector(
                                    onTap: () => controller.noteClickByAll(index),
                                    child: Container(
                                      clipBehavior: Clip.antiAlias,
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFF6F6F6),
                                        borderRadius: BorderRadius.circular(4.whs),
                                      ),
                                      child: IntrinsicHeight(
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: 4,
                                              color: Get.theme.primaryColor,
                                            ),
                                            Gap(8.whs),
                                            Flexible(
                                              child: Padding(
                                                padding: EdgeInsets.all(6.whs),
                                                child: Obx(() => Text(
                                                      controller.notesMap[controller.subtitles[index].subtitleIndex]?.content ?? "",
                                                      style: TextStyle(fontSize: 16.whs),
                                                    )),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Divider(),
                        ],
                      );
                    },
                  ),
          )),
        ],
      ),
    );
  }
}
