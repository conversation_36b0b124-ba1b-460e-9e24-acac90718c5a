import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/model/user_login_resp/user.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/oss.dart';

class MineController extends GetxController {
  var user = User().obs;
  
  @override
  void onInit() {
    super.onInit();
    // 使用Config中的用户信息
    user.value = Config().currentUser.value;
    // 监听用户信息变化
    ever(Config().currentUser, (User user) {
      this.user.value = user;
    });
  }

  void uploadAvatar() async {
    final picker = ImagePicker();
    final image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      final url = await OssUtil().uploadUserAvatarFile(image.path);
      user.value.avatar = url;
      Net.getRestClient().updateUserInfo({'avatar': url}).then((value) {
        user.value = value.data;
        // 更新Config中的用户信息
        Config().currentUser.value = value.data;
      });
      user.refresh();
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void testError() async {
    throw Exception("this is test Exception in mine");
  }

  void testSubtitle() {
    Get.toNamed(Routes.SUBTITLE_PREVIEW, arguments: {'subtitlePath': "http://47.96.149.216:3000/images/1729938492512043675_896991.srt"});
  }

  void testAliyunPan() async {
    Get.toNamed(Routes.ALIYUNPAN);
  }
}
