import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:lsenglish/utils/oss.dart';
import 'package:path_provider/path_provider.dart';

void logger(String message) {
  Logger().log(message);
}

class Logger {
  static final Logger _instance = Logger._internal();
  static const int maxLogSize = 5 * 1024 * 1024; // 5MB
  final List<String> _logBuffer = [];
  File? _logFile;
  Timer? _timer;

  factory Logger() {
    return _instance;
  }

  Logger._internal() {
    _startLogTimer();
  }

  Future<void> initLogFile() async {
    final directory = await getApplicationDocumentsDirectory();
    _logFile = File('${directory.path}/logs.txt');
    if (!await _logFile!.exists()) {
      await _logFile!.create();
    }
  }

  void _startLogTimer() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) => _flushLogs());
  }

  Future<void> log(String message) async {
    debugPrint(message);
    final timestamp = DateTime.now().toIso8601String();
    _logBuffer.add('$timestamp: $message\n');
  }

  Future<void> _flushLogs() async {
    if (_logBuffer.isEmpty || _logFile == null) return;

    final logEntries = _logBuffer.join();
    _logBuffer.clear();

    final currentSize = await _logFile!.length();
    if (currentSize + logEntries.length > maxLogSize) {
      final lines = await _logFile!.readAsLines();
      while (lines.isNotEmpty && (await _logFile!.length()) > maxLogSize) {
        lines.removeAt(0);
      }
      await _logFile!.writeAsString('${lines.join('\n')}\n');
    }

    await _logFile!.writeAsString(logEntries, mode: FileMode.append);
  }

  Future<void> clearLogs() async {
    await _logFile?.writeAsString('');
  }

  Future<void> uploadLogFile() async {
    await _flushLogs();

    debugPrint('Uploading log file...');
    final filePath = _logFile?.path;
    if (filePath == null) return;
    await OssUtil().uploadUserLogFile(filePath);
    clearLogs();
  }
}

void disposeLogger() {
  Logger()._timer?.cancel();
}
