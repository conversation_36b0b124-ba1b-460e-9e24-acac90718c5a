import 'dart:async';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:flutter/foundation.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

class FileUtils {
  FileUtils._privateConstructor();
  static final FileUtils _instance = FileUtils._privateConstructor();
  factory FileUtils() {
    return _instance;
  }
  Future<void> ensureDirectory() async {
    // 获取并检查基础保存目录
    final Directory baseDir = await getSaveDir();
    await ensureDirectoryExists(baseDir);

    // 获取并检查字幕保存目录
    final Directory subtitleDir = await getSaveSubtitleDir();
    await ensureDirectoryExists(subtitleDir);

    final Directory videoThumbDir = await getVideoThumbDir();
    await ensureDirectoryExists(videoThumbDir);

    // 获取并检查录音保存目录
    final Directory recordDir = await getRecordDir();
    await ensureDirectoryExists(recordDir);

    await Logger().initLogFile();

    // dingSoundPath = await loadAsset("assets/sounds/ding.mp3");
  }

  // 通用方法来确保单个目录存在
  Future<void> ensureDirectoryExists(Directory dir) async {
    if (!await dir.exists()) {
      await dir.create(recursive: true); // 如果目录不存在，创建它
    }
  }

//自己的文件目录 包含视频、字幕
  Future<Directory> getSaveDir() async {
    if (Platform.isAndroid || Platform.isMacOS) {
      var dir = await getApplicationDocumentsDirectory();
      return Directory("${dir.path}/ls");
    }
    return await getApplicationDocumentsDirectory();
  }

  Future<Directory> getSaveSubtitleDir() async {
    var saveDir = await getSaveDir();
    return Directory("${saveDir.path}/subtitles");
  }

  Future<Directory> getVideoThumbDir() async {
    var saveDir = await getSaveDir();
    return Directory("${saveDir.path}/cover");
  }

  Future<Directory> getRecordDir() async {
    var saveDir = await getSaveDir();
    return Directory("${saveDir.path}/record");
  }

  Future<String> getStringByDir(Future<Directory> dir) async {
    return (await dir).path;
  }

  Future<String> saveTmpAudioServiceCover(Uint8List uint8list) async {
    var saveDir = await getApplicationCacheDirectory();
    File tempFile = File("${saveDir.path}/getTmpAudioServiceCoverPathImage.jpeg");
    tempFile.writeAsBytes(uint8list);
    return tempFile.path;
  }

  Future<String> moveFileToDocuments(String path) async {
    if (path.isEmpty) {
      return "";
    }
    String tempFilePath = path; // 获取临时文件路径
    File tempFile = File(tempFilePath);

    // 获取应用的 Documents 目录路径
    Directory documentsDirectory = await getSaveDir();
    String documentsPath = documentsDirectory.path;

    // 创建新的文件路径
    String newFilePath = '$documentsPath/${basename(path)}';

    // 将文件从临时位置移动到 Documents 目录
    File newFile = await tempFile.rename(newFilePath);

    logger('File moved to: $newFilePath');
    return newFile.path; // 返回新的文件路径
  }

  Future<String> copyFileToDocuments(String path) async {
    if (path.isEmpty) {
      return "";
    }
    File tempFile = File(path);
    // 获取应用的 Documents 目录路径
    Directory documentsDirectory = await getSaveDir();
    String documentsPath = documentsDirectory.path;
    // 创建新的文件路径
    String newFilePath = '$documentsPath/${basename(path)}';
    logger("newFilePath = $newFilePath");
    await tempFile.copy(newFilePath);
    return newFilePath;
  }

  String getFileSize(int fileSize) {
    String str = '';

    if (fileSize < 1024) {
      str = '${fileSize.toStringAsFixed(2)}B';
    } else if (1024 <= fileSize && fileSize < 1048576) {
      str = '${(fileSize / 1024).toStringAsFixed(2)}KB';
    } else if (1048576 <= fileSize && fileSize < 1073741824) {
      str = '${(fileSize / 1024 / 1024).toStringAsFixed(2)}MB';
    }

    return str;
  }

// 计算文件夹内 文件、文件夹的数量，以 . 开头的除外
  int calculateFilesCountByFolder(Directory path) {
    var dir = path.listSync();
    int count = dir.length - _calculatePointBegin(dir);

    return count;
  }

// 计算以 . 开头的文件、文件夹总数
  int _calculatePointBegin(List<FileSystemEntity> fileList) {
    int count = 0;
    for (var v in fileList) {
      if (p.basename(v.path).substring(0, 1) == '.') count++;
    }

    return count;
  }

// 获取当前路径下的文件/文件夹
  List<FileSystemEntity> getCurrentPathFiles(String path) {
    var currentFiles = <FileSystemEntity>[];
    try {
      Directory currentDir = Directory(path);
      List<FileSystemEntity> files = [];
      List<FileSystemEntity> folder = [];

      // 遍历所有文件/文件夹
      for (var v in currentDir.listSync()) {
        // 去除以 .开头的文件/文件夹
        if (p.basename(v.path).substring(0, 1) == '.') {
          continue;
        }
        if (FileSystemEntity.isFileSync(v.path)) {
          files.add(v);
        } else {
          folder.add(v);
        }
      }

      // 排序
      files.sort((a, b) => a.path.toLowerCase().compareTo(b.path.toLowerCase()));
      folder.sort((a, b) => a.path.toLowerCase().compareTo(b.path.toLowerCase()));

      currentFiles.clear();
      currentFiles.addAll(folder);
      currentFiles.addAll(files);
      return currentFiles;
    } catch (e) {
      return [];
    }
  }

  bool isSubtitleFile(String filepath) {
    return '.vtt.srt.ttml.dfxp.ass'.contains(extension(filepath.toLowerCase()));
  }

  Future<bool> isZipFile(String filePath) async {
    File file = File(filePath);

    if (await file.exists()) {
      // 打开文件
      RandomAccessFile raf = await file.open(mode: FileMode.read);

      // 读取前4个字节
      Uint8List bytes = await raf.read(4);
      await raf.close();

      // 转换为16进制字符串
      String fileSignature = bytes.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join().toUpperCase();

      // 检查签名是否匹配ZIP文件
      return fileSignature == '504B0304' || fileSignature == '504B0506' || fileSignature == '504B0708';
    }

    return false;
  }

  static Future<String> copyFileToDirectory(String path, Directory targetDirectory) async {
    // 获取源文件名
    String filename = basename(path);
    // 创建目标文件的路径
    String targetPath = join(targetDirectory.path, filename);
    // 复制文件

    File newFile = await File(path).copy(targetPath);
    return newFile.path;
  }

  Future<List<String>> getAllFilePaths(String directoryPath) async {
    Directory dir = Directory(directoryPath);
    List<String> filePaths = [];

    // 检查目录是否存在
    if (await dir.exists()) {
      // 使用async*生成器和yield*来递归遍历所有文件
      await for (var entity in dir.list(recursive: true, followLinks: false)) {
        if (entity is File) {
          filePaths.add(entity.path); // 添加文件路径到列表
        }
      }
    }
    return filePaths;
  }

  String extractShowNameForSearch(String filename) {
    // 首先尝试匹配包含季数和集数的格式
    RegExp patternWithSeason = RegExp(r'^(.+?)\.S\d+E\d+');
    RegExpMatch? match = patternWithSeason.firstMatch(filename);

    // 如果没有匹配到季数和集数，尝试只提取节目名称
    if (match == null) {
      RegExp patternGeneral = RegExp(r'^(.+?)\.(mkv|avi|mp4)$');
      match = patternGeneral.firstMatch(filename);
    }

    // 如果找到匹配，处理节目名称
    if (match != null) {
      String showName = match.group(1)!.replaceAll('.', ' ');
      String queryReadyShowName = showName.trim().replaceAll(' ', '+');
      return queryReadyShowName;
    } else {
      // 如果没有找到任何匹配，返回空字符串
      return '';
    }
  }

  Future<List<Uint8List>> readFileIntoUint8List(String filepath, {int chunkSize = 4096}) async {
    // 打开文件
    File file = File(filepath);
    RandomAccessFile raf = await file.open(mode: FileMode.read);
    List<Uint8List> chunks = [];
    bool eof = false;

    while (!eof) {
      // 读取固定大小的数据块
      Uint8List buffer = await raf.read(chunkSize);
      if (buffer.length < chunkSize) {
        eof = true; // 如果读取的数据少于chunkSize，说明到了文件尾部
      }
      if (buffer.isNotEmpty) {
        chunks.add(buffer);
      }
    }

    // 关闭文件
    await raf.close();
    return chunks;
  }

// 合并Uint8List
  Uint8List combineUint8Lists(List<Uint8List> lists) {
    int totalLength = lists.fold(0, (sum, list) => sum + list.length);
    Uint8List result = Uint8List(totalLength);
    int offset = 0;
    for (Uint8List list in lists) {
      result.setRange(offset, offset + list.length, list);
      offset += list.length;
    }
    return result;
  }

  Future<void> savePCMAsWav(Uint8List audioData, String toFile) async {
    String tempPcmPath = toFile.replaceAll(".aac", ".pcm");
    File tempPcmFile = File(tempPcmPath);
    await tempPcmFile.writeAsBytes(audioData);
    String outputPath = toFile;
    // 确保 -ar 参数（代表采样率）和 -ac 参数（代表声道数）与你的源 PCM 数据相匹配。
    // 添加降噪过滤器afftdn，并设置降噪参数，这里使用默认参数
    String ffmpegCmd = '-y -f s16le -ar 16000 -ac 1 -i $tempPcmPath -af "afftdn" -c:a aac -b:a 64k $outputPath';
    await FFmpegKit.execute(ffmpegCmd).then((session) async {
      final returnCode = await session.getReturnCode();
      if (ReturnCode.isSuccess(returnCode)) {
        logger("转换成功，文件保存在: $outputPath");
      } else {
        logger("转换失败: $returnCode");
      }
    });
    await tempPcmFile.delete();
  }

  Future<String> calculateFileHash(String filePath) async {
    if (filePath.isEmpty) {
      return "";
    }
    var file = File(filePath);

    // 获取文件大小
    var fileSize = await file.length();

    // 判断文件大小是否超过10MB
    const int maxFileSize = 10 * 1024 * 1024; // 10MB in bytes
    if (fileSize > maxFileSize) {
      return '';
    }
    var bytes = await file.readAsBytes();
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<List<String>> findFilesWithHashInDefaultDir(String targetHash) async {
    var path = (await getSaveDir()).path;
    return findFilesWithHash(path, targetHash);
  }

  Future<List<String>> findFilesWithHash(String directoryPath, String targetHash) async {
    final directory = Directory(directoryPath);

    if (!await directory.exists()) {
      throw Exception('Directory not found');
    }

    final matchingFiles = <String>[];

    await for (var entity in directory.list(recursive: true, followLinks: false)) {
      if (entity is File) {
        try {
          final hash = await calculateFileHash(entity.path);
          if (hash == targetHash) {
            matchingFiles.add(entity.path);
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error calculating hash for ${entity.path}: $e');
          }
          return [];
        }
      }
    }

    if (matchingFiles.isEmpty) {
      if (kDebugMode) {
        print('No files found with hash: $targetHash');
      }
      return [];
    } else {
      if (kDebugMode) {
        print('Files with hash $targetHash:');
      }
      return matchingFiles;
    }
  }

  bool isVideoFile(String filePath) {
    final mimeType = lookupMimeType(filePath);
    return mimeType != null && mimeType.startsWith('video/');
  }

  bool isTrashFile(String? videoPath) {
    return Platform.isIOS && videoPath?.contains(".Trash") == true;
  }

  Future<String?> findVideoFilePath(String directoryPath, String fileName) async {
    final directory = Directory(directoryPath);

    try {
      // 检查目录是否存在
      if (await directory.exists()) {
        // 获取目录下的所有文件和子目录
        final entities = directory.list(recursive: true, followLinks: false);
        await for (var entity in entities) {
          if (entity is File && isVideoFile(entity.path) && !isTrashFile(entity.path) && basenameWithoutExtension(entity.path) == fileName) {
            return entity.path;
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error while searching for file: $e');
      }
    }
    // 如果没有找到文件，返回 null
    return null;
  }

  Future<String> renameFile(String oldPath, String newName) async {
    final file = File(oldPath);

    try {
      if (await file.exists()) {
        final directory = p.dirname(oldPath);
        final extension = p.extension(oldPath);
        final newPath = p.join(directory, '$newName$extension');
        await file.rename(newPath);
        if (kDebugMode) {
          print('File renamed successfully to $newPath');
        }

        return newPath;
      } else {
        if (kDebugMode) {
          print('File does not exist.');
        }
        return "";
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error renaming file: $e');
      }
      return "";
    }
  }

  String generateTempFilePath(String originalPath, String appendName) {
    // 获取文件的目录和扩展名
    final directory = p.dirname(originalPath);
    final basename = p.basenameWithoutExtension(originalPath);
    final extension = p.extension(originalPath);

    // 生成新文件名
    final newFilename = '$basename$appendName$extension';

    // 合成新的完整路径
    return p.join(directory, newFilename);
  }
}
