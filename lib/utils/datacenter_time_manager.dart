import 'dart:convert';
import 'dart:async';

import 'package:get_storage/get_storage.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/subtitle/subtitle.dart';

import '../net/net.dart';
import 'obs.dart';
import 'toast.dart';

/// 学习行为事件类型
enum LearningEventType {
  playStart('play_start'),
  playEnd('play_end'),
  recordStart('record_start'),
  recordEnd('record_end'),
  pause('pause'),
  resume('resume');

  const LearningEventType(this.value);
  final String value;
}

/// 句子学习记录
class SentenceLearningRecord {
  final int subtitleStartTime;
  final int subtitleEndTime;
  final int subtitleDuration;

  int playCount = 0;
  int playTotalDuration = 0;
  int recordCount = 0;
  int recordTotalDuration = 0;
  int lastInteractionTime = 0;

  SentenceLearningRecord({
    required this.subtitleStartTime,
    required this.subtitleEndTime,
    required this.subtitleDuration,
  });

  /// 播放平均时长
  double get playAvgDuration => playCount > 0 ? playTotalDuration / playCount : 0.0;

  /// 录音平均时长
  double get recordAvgDuration => recordCount > 0 ? recordTotalDuration / recordCount : 0.0;

  Map<String, dynamic> toJson() => {
        'subtitleStartTime': subtitleStartTime,
        'subtitleEndTime': subtitleEndTime,
        'subtitleDuration': subtitleDuration,
        'playCount': playCount,
        'playTotalDuration': playTotalDuration,
        'playAvgDuration': playAvgDuration,
        'recordCount': recordCount,
        'recordTotalDuration': recordTotalDuration,
        'recordAvgDuration': recordAvgDuration,
        'lastInteractionTime': lastInteractionTime,
      };

  factory SentenceLearningRecord.fromJson(Map<String, dynamic> json) => SentenceLearningRecord(
        subtitleStartTime: json['subtitleStartTime'] ?? 0,
        subtitleEndTime: json['subtitleEndTime'] ?? 0,
        subtitleDuration: json['subtitleDuration'] ?? 0,
      )
        ..playCount = json['playCount'] ?? 0
        ..playTotalDuration = json['playTotalDuration'] ?? 0
        ..recordCount = json['recordCount'] ?? 0
        ..recordTotalDuration = json['recordTotalDuration'] ?? 0
        ..lastInteractionTime = json['lastInteractionTime'] ?? 0;
}

/// 学习会话数据
class LearningSessionData {
  final String resourceId;
  final int resourceType;
  final int sessionStartTime;
  final int sessionEndTime;
  final int totalSessionDuration; // 会话总时长，毫秒
  final int totalPlayDuration;    // 播放总时长，毫秒
  final int totalRecordDuration;  // 录音总时长，毫秒
  final int lsTimes;

  LearningSessionData({
    required this.resourceId,
    required this.resourceType,
    required this.sessionStartTime,
    required this.sessionEndTime,
    required this.totalSessionDuration,
    required this.totalPlayDuration,
    required this.totalRecordDuration,
    required this.lsTimes,
  });

  Map<String, dynamic> toJson() => {
        'resourceId': resourceId,
        'resourceType': resourceType,
        'sessionStartTime': sessionStartTime,
        'sessionEndTime': sessionEndTime,
        'totalSessionDuration': totalSessionDuration,
        'totalPlayDuration': totalPlayDuration,
        'totalRecordDuration': totalRecordDuration,
        'lsTimes': lsTimes,
      };

  factory LearningSessionData.fromJson(Map<String, dynamic> json) => LearningSessionData(
        resourceId: json['resourceId'] ?? '',
        resourceType: json['resourceType'] ?? 2,
        sessionStartTime: json['sessionStartTime'] ?? 0,
        sessionEndTime: json['sessionEndTime'] ?? 0,
        totalSessionDuration: json['totalSessionDuration'] ?? 0,
        totalPlayDuration: json['totalPlayDuration'] ?? 0,
        totalRecordDuration: json['totalRecordDuration'] ?? 0,
        lsTimes: json['lsTimes'] ?? 0,
      );
}

/// 学习数据上传模型
class LearningDataUpload {
  final LearningSessionData sessionData;
  final List<SentenceLearningRecord> sentenceRecords;

  LearningDataUpload({
    required this.sessionData,
    required this.sentenceRecords,
  });

  Map<String, dynamic> toJson() => {
        'sessionData': sessionData.toJson(),
        'sentenceRecords': sentenceRecords.map((e) => e.toJson()).toList(),
      };

  factory LearningDataUpload.fromJson(Map<String, dynamic> json) => LearningDataUpload(
        sessionData: LearningSessionData.fromJson(json['sessionData'] ?? {}),
        sentenceRecords: (json['sentenceRecords'] as List? ?? []).map((e) => SentenceLearningRecord.fromJson(e)).toList(),
      );
}

/// 学习分析结果
class LearningAnalysisResult {
  final double masteryScore;
  final List<String> recommendedSentences;
  final Map<String, dynamic> learningInsights;

  LearningAnalysisResult({
    required this.masteryScore,
    required this.recommendedSentences,
    required this.learningInsights,
  });

  factory LearningAnalysisResult.fromJson(Map<String, dynamic> json) => LearningAnalysisResult(
        masteryScore: (json['masteryScore'] ?? 0.0).toDouble(),
        recommendedSentences: List<String>.from(json['recommendedSentences'] ?? []),
        learningInsights: json['learningInsights'] ?? {},
      );
}

/// 当前活动状态
class CurrentActivity {
  final LearningEventType eventType;
  final String sentenceId;
  final int startTime;

  CurrentActivity({
    required this.eventType,
    required this.sentenceId,
    required this.startTime,
  });
}

/// 现代化的学习数据统计管理器
class DataCenterTimeManager {
  DataCenterTimeManager._internal();
  static final DataCenterTimeManager _instance = DataCenterTimeManager._internal();
  factory DataCenterTimeManager() => _instance;

  // 核心数据
  final Map<String, SentenceLearningRecord> _sentenceRecords = {};
  CurrentActivity? _currentActivity;

  // 学习会话数据
  String _resourceId = '';
  int _resourceType = 2;
  int _sessionStartTime = 0;
  int _sessionEndTime = 0;
  int _lsTimes = 0;
  bool _isPaused = false;

  // 智能存储相关
  Timer? _backupTimer;
  bool _hasUnsavedChanges = false;

  // 存储键
  static const String _sentenceRecordsKey = 'DataCenterTimeManager_v2_sentenceRecords';
  static const String _sessionDataKey = 'DataCenterTimeManager_v2_sessionData';

  // 配置
  static const Duration _backupInterval = Duration(seconds: 30);

  /// 初始化管理器
  Future<void> initialize() async {
    logger("DataCenterTimeManager initialize start");

    try {
      // 加载本地数据
      await _loadLocalData();

      // 检查是否有未上传的数据并自动上传
      await _checkAndUploadPendingData();

      // 启动定期备份
      _startBackupTimer();

      logger("DataCenterTimeManager initialize completed successfully");
    } catch (e, stackTrace) {
      logger("DataCenterTimeManager initialize failed: $e");
      logger("StackTrace: $stackTrace");
      // 初始化失败时清理数据，避免脏数据
      _clearInMemoryData();
    }
  }

  /// 检查并上传待处理的数据
  Future<void> _checkAndUploadPendingData() async {
    if (_sessionStartTime == 0 || _resourceId.isEmpty) {
      logger("DataCenterTimeManager: 没有待上传的数据");
      return;
    }

    logger("DataCenterTimeManager: 发现未上传数据，开始自动上传");

    try {
      await uploadLearningData();
      logger("DataCenterTimeManager: 自动上传成功");
    } catch (e) {
      clear();
      logger("DataCenterTimeManager: 自动上传失败，数据移除: $e");
    }
  }

  /// 加载本地数据
  Future<void> _loadLocalData() async {
    logger("DataCenterTimeManager _loadLocalData");

    try {
      // 加载句子学习记录
      final sentenceRecordsJson = GetStorage().read(_sentenceRecordsKey) ?? {};
      _sentenceRecords.clear();
      (sentenceRecordsJson as Map<String, dynamic>).forEach((key, value) {
        _sentenceRecords[key] = SentenceLearningRecord.fromJson(value);
      });

      // 加载会话数据
      final sessionDataJson = GetStorage().read(_sessionDataKey) ?? {};
      _resourceId = sessionDataJson['resourceId'] ?? '';
      _resourceType = sessionDataJson['resourceType'] ?? 2;
      _sessionStartTime = sessionDataJson['sessionStartTime'] ?? 0;
      _sessionEndTime = sessionDataJson['sessionEndTime'] ?? 0;
      _lsTimes = sessionDataJson['lsTimes'] ?? 0;

      logger("DataCenterTimeManager _loadLocalData: resourceId=$_resourceId, sentenceRecords=${_sentenceRecords.length}");
    } catch (e) {
      logger("DataCenterTimeManager _loadLocalData error: $e");
      _clearInMemoryData();
    }
  }

  /// 清理内存数据
  void _clearInMemoryData() {
    _sentenceRecords.clear();
    _currentActivity = null;
    _resourceId = '';
    _resourceType = 2;
    _sessionStartTime = 0;
    _sessionEndTime = 0;
    _lsTimes = 0;
    _hasUnsavedChanges = false;
  }

  /// 启动定期备份定时器
  void _startBackupTimer() {
    logger("DataCenterTimeManager _startBackupTimer");
    _backupTimer?.cancel();
    _backupTimer = Timer.periodic(_backupInterval, (timer) {
      if (_hasUnsavedChanges && !_isPaused) {
        _saveToLocal();
      }
    });
  }

  /// 立即保存到本地存储
  Future<void> _saveToLocal() async {
    if (!_hasUnsavedChanges) return;

    logger("DataCenterTimeManager _saveToLocal");

    try {
      // 保存句子学习记录
      final sentenceRecordsJson = _sentenceRecords.map((key, value) => MapEntry(key, value.toJson()));
      GetStorage().write(_sentenceRecordsKey, sentenceRecordsJson);

      // 保存会话数据
      _sessionEndTime = DateTime.now().millisecondsSinceEpoch; // 改为毫秒
      final sessionData = {
        'resourceId': _resourceId,
        'resourceType': _resourceType,
        'sessionStartTime': _sessionStartTime,
        'sessionEndTime': _sessionEndTime,
        'lsTimes': _lsTimes,
      };
      GetStorage().write(_sessionDataKey, sessionData);

      _hasUnsavedChanges = false;
      logger("DataCenterTimeManager _saveToLocal completed");
    } catch (e) {
      logger("DataCenterTimeManager _saveToLocal error: $e");
    }
  }

  /// 开始新的学习会话
  void beginSession({
    required String resourceId,
    required int resourceType,
    required int lsTimes,
  }) {
    logger("DataCenterTimeManager beginSession: resourceId=$resourceId, resourceType=$resourceType, lsTimes=$lsTimes");

    // 如果有正在进行的会话，先上传数据
    if (_sessionStartTime > 0 && _resourceId.isNotEmpty) {
      logger("DataCenterTimeManager: 检测到正在进行的会话，先上传数据");
      uploadLearningData().catchError((e) {
        logger("DataCenterTimeManager: 上传之前会话数据失败: $e");
      });
    }

    // 清理之前的数据
    _clearInMemoryData();

    // 设置新会话
    _resourceId = resourceId;
    _resourceType = resourceType;
    _lsTimes = lsTimes;
    _sessionStartTime = DateTime.now().millisecondsSinceEpoch; // 改为毫秒
    _isPaused = false;
    _hasUnsavedChanges = true;

    // 立即保存
    _saveToLocal();

    logger("DataCenterTimeManager beginSession completed");
  }

  /// 获取学习时长（毫秒）
  int getSessionDuration() {
    if (_sessionStartTime == 0) return 0;
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    return currentTime - _sessionStartTime;
  }

  /// 暂停学习
  void pauseSession() {
    if (_isPaused) return;

    logger("DataCenterTimeManager pauseSession");
    _isPaused = true;

    _hasUnsavedChanges = true;
    _saveToLocal(); // 暂停时立即保存
  }

  /// 恢复学习
  void resumeSession() {
    if (!_isPaused) return;

    logger("DataCenterTimeManager resumeSession");
    _isPaused = false;

    _hasUnsavedChanges = true;
  }

  /// 开始活动（播放或录音）
  void _startActivity({
    required LearningEventType eventType,
    required int subtitleStartTime,
    required int subtitleEndTime,
    required bool isLSMode,
    required int startTime,
    int? subtitleIndex,
  }) {
    final sentenceId = '${subtitleStartTime}_$subtitleEndTime';

    logger("DataCenterTimeManager _startActivity: eventType=$eventType, sentenceId=$sentenceId, isLSMode=$isLSMode");

    // 统一：只记录当前活动，等待结束时计算时长（无论LS模式还是普通模式）
    _currentActivity = CurrentActivity(
      eventType: eventType,
      sentenceId: sentenceId,
      startTime: startTime,
    );
  }

  /// 结束活动（播放或录音）
  void _endActivity({
    required LearningEventType eventType,
    required int endTime,
    int subtitleStartTime = 0,
    int subtitleEndTime = 0,
  }) {
    final duration = _currentActivity != null ? endTime - _currentActivity!.startTime : 0;

    if (_currentActivity == null) return;

    final sentenceId = _currentActivity!.sentenceId;

    logger("DataCenterTimeManager _endActivity: sentenceId=$sentenceId, duration=$duration");

    if (duration > 0) {
      // 获取或创建句子学习记录
      final record = _sentenceRecords.putIfAbsent(
        sentenceId,
        () => SentenceLearningRecord(
          subtitleStartTime: subtitleStartTime,
          subtitleEndTime: subtitleEndTime,
          subtitleDuration: subtitleEndTime - subtitleStartTime,
        ),
      );

      if (_currentActivity!.eventType == LearningEventType.playStart) {
        record.playCount += 1;
        record.playTotalDuration += duration;
      } else if (_currentActivity!.eventType == LearningEventType.recordStart) {
        record.recordCount += 1;
        record.recordTotalDuration += duration;
      }

      record.lastInteractionTime = endTime ~/ 1000; // 转换为秒

      logger("DataCenterTimeManager _endActivity: sentenceId=$sentenceId, playCount=${record.playCount}, recordCount=${record.recordCount}");

      _hasUnsavedChanges = true;
    }

    _currentActivity = null;
  }

  /// 获取播放统计（兼容旧接口）
  List<Map<String, dynamic>> getPlayStats() {
    return _sentenceRecords.values
        .map((record) => {
              'subtitleStartTime': record.subtitleStartTime,
              'subtitleEndTime': record.subtitleEndTime,
              'count': record.playCount,
              'totalDuration': record.playTotalDuration,
            })
        .toList();
  }

  /// 获取录音统计（兼容旧接口）
  List<Map<String, dynamic>> getRecordStats() {
    return _sentenceRecords.values
        .map((record) => {
              'subtitleStartTime': record.subtitleStartTime,
              'subtitleEndTime': record.subtitleEndTime,
              'count': record.recordCount,
              'totalDuration': record.recordTotalDuration,
            })
        .toList();
  }

  /// 清理统计数据
  void clearStats() {
    logger("DataCenterTimeManager clearStats");
    _clearInMemoryData();
    _hasUnsavedChanges = true;
    _saveToLocal();
  }

  /// 清理本地数据
  Future<void> _clearLocalData() async {
    logger("DataCenterTimeManager _clearLocalData");
    GetStorage().remove(_sentenceRecordsKey);
    GetStorage().remove(_sessionDataKey);
  }

  /// 清理所有数据（包括内存和本地存储）
  Future<void> clear() async {
    logger("DataCenterTimeManager clear");
    _clearInMemoryData();
    await _clearLocalData();
  }

  /// 获取会话开始时间
  int get startTime => _sessionStartTime;

  /// 获取会话结束时间
  int get endTime => _sessionEndTime;

  /// 兼容旧接口的方法
  void pause() => pauseSession();
  void resume() => resumeSession();
  void begin(String? resourceId, int? resourceType, int? lsTimes) {
    if (resourceId != null) {
      beginSession(
        resourceId: resourceId,
        resourceType: resourceType ?? 2,
        lsTimes: lsTimes ?? 0,
      );
    }
  }

  int getDuration() => getSessionDuration();

  /// 上传学习数据到后端（新版本API）
  Future<void> uploadLearningData() async {
    logger("DataCenterTimeManager uploadLearningData start");

    if (_resourceId.isEmpty || _sessionStartTime == 0) {
      throw Exception("没有有效的学习会话数据");
    }

    final duration = getSessionDuration(); // duration 现在是毫秒
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    final totalPlayDuration = _sentenceRecords.values.fold<int>(0, (sum, record) => sum + record.playTotalDuration);
    final totalRecordDuration = _sentenceRecords.values.fold<int>(0, (sum, record) => sum + record.recordTotalDuration);

    // 验证数据合理性
    if (duration > 60 * 60 * 20 * 1000) { // 20小时，毫秒
      throw Exception("学习时长异常：$duration毫秒");
    }
    // if (duration < 10 * 1000) { // 少于10秒，毫秒
    //   throw Exception("学习时长太短：${duration}毫秒");
    // }

    // 构建上传数据
    final sessionData = LearningSessionData(
      resourceId: _resourceId,
      resourceType: _resourceType,
      sessionStartTime: _sessionStartTime,
      sessionEndTime: currentTime,
      totalSessionDuration: duration,
      totalPlayDuration: totalPlayDuration,
      totalRecordDuration: totalRecordDuration,
      lsTimes: _lsTimes,
    );

    final uploadData = LearningDataUpload(
      sessionData: sessionData,
      sentenceRecords: _sentenceRecords.values.toList(),
    );

    final dataMap = uploadData.toJson();

    logger("DataCenterTimeManager uploadLearningData: 准备上传 ${_sentenceRecords.length} 条句子记录");
    logger("DataCenterTimeManager uploadData: ${jsonEncode(dataMap)}");

    try {
      // 临时使用现有的API接口，后续可以扩展为新的接口
      await Net.getRestClient().addDataEpisode(dataMap);

      final learnDuration = (duration / 1000 / 60).round(); // 转为分钟
      logger("DataCenterTimeManager uploadLearningData: 上传成功，本次学习$learnDuration分钟");
      toastInfo("本次已学习$learnDuration分钟");

      // 上传成功后清理数据
      await clear();
      clearStats();
      ObsUtil().updateDataCenter.value = DateTime.now().millisecond;

      logger("DataCenterTimeManager uploadLearningData: 数据已清理");
    } catch (e, stackTrace) {
      logger("DataCenterTimeManager uploadLearningData: 上传失败 $e");
      logger("StackTrace: $stackTrace");
      rethrow;
    }
  }

  /// 兼容旧接口的上传方法
  Future<void> addDataEpisodeAndUpload({
    required String resourceId,
    required int resourceType,
    required int currentLsTimes,
  }) async {
    logger("DataCenterTimeManager addDataEpisodeAndUpload (legacy): resourceId=$resourceId");

    // 如果当前会话的资源ID不匹配，更新会话信息
    if (_resourceId != resourceId) {
      _resourceId = resourceId;
      _resourceType = resourceType;
      _lsTimes = currentLsTimes;
      _hasUnsavedChanges = true;
    }

    try {
      await uploadLearningData();
    } catch (e) {
      logger("DataCenterTimeManager addDataEpisodeAndUpload (legacy): 上传失败 $e");
      // 兼容旧版本，不抛出异常
    }
  }

  /// 记录统计数据的内部方法
  void _recordStat({
    required bool isPlay,
    required bool isStart,
    required bool isLSMode,
    required int subtitleStartTime,
    required int subtitleEndTime,
    required int now,
    int? subtitleIndex,
  }) {
    final eventType = isPlay
        ? (isStart ? LearningEventType.playStart : LearningEventType.playEnd)
        : (isStart ? LearningEventType.recordStart : LearningEventType.recordEnd);

    if (isStart) {
      _startActivity(
        eventType: eventType,
        subtitleStartTime: subtitleStartTime,
        subtitleEndTime: subtitleEndTime,
        isLSMode: isLSMode,
        startTime: now,
        subtitleIndex: subtitleIndex,
      );
    } else {
      _endActivity(
        eventType: eventType,
        endTime: now,
        subtitleStartTime: subtitleStartTime,
        subtitleEndTime: subtitleEndTime,
      );
    }
  }

  /// 记录统计数据（公共接口）
  void recordStatWithSubtitles({
    required bool isPlay,
    required bool isStart,
    required int index,
    required bool isLSMode,
    required int now,
    required List<Subtitle> subtitles,
  }) {
    int subtitleStartTime = 0;
    int subtitleEndTime = 0;

    if (index >= 0 && index < subtitles.length) {
      subtitleStartTime = subtitles[index].start.inMilliseconds;
      subtitleEndTime = subtitles[index].end.inMilliseconds;
    }

    _recordStat(
      isPlay: isPlay,
      isStart: isStart,
      isLSMode: isLSMode,
      subtitleStartTime: subtitleStartTime,
      subtitleEndTime: subtitleEndTime,
      now: now,
      subtitleIndex: index,
    );
  }

  /// 获取学习统计摘要
  Map<String, dynamic> getLearningStatsSummary() {
    final totalSentences = _sentenceRecords.length;
    final totalPlayCount = _sentenceRecords.values.fold<int>(0, (sum, record) => sum + record.playCount);
    final totalRecordCount = _sentenceRecords.values.fold<int>(0, (sum, record) => sum + record.recordCount);
    return {
      'totalSentences': totalSentences,
      'totalPlayCount': totalPlayCount,
      'totalRecordCount': totalRecordCount,
      'sessionDuration': getSessionDuration(), // 毫秒
    };
  }

  /// 销毁管理器，清理资源
  void dispose() {
    logger("DataCenterTimeManager dispose");
    _backupTimer?.cancel();
    _backupTimer = null;

    // 最后一次保存
    if (_hasUnsavedChanges) {
      _saveToLocal();
    }
  }
}
