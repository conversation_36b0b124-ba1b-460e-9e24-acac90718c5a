import 'package:lsenglish/config/config.dart';

class PlayerMenu {
  final int id;
  final String title;

  PlayerMenu({
    required this.id,
    required this.title,
  });
}

class PlayerMenuManager {
  // Private constructor
  PlayerMenuManager._();

  // Single instance
  static final PlayerMenuManager _instance = PlayerMenuManager._();

  // Factory constructor to return the same instance
  factory PlayerMenuManager() => _instance;

  // Default order of menu items
  final List<PlayerMenu> _playerMenuItems = [
    PlayerMenu(id: 1, title: 'Note'),
    PlayerMenu(id: 2, title: 'AI'),
    PlayerMenu(id: 3, title: 'Collect'),
    PlayerMenu(id: 4, title: 'Rate'),
    PlayerMenu(id: 5, title: 'Eye'),
    PlayerMenu(id: 6, title: 'Translate'),
    PlayerMenu(id: 7, title: 'EditSubtitle'),
    PlayerMenu(id: 8, title: 'Skip'),
    PlayerMenu(id: 9, title: 'More'),
  ];
  List<PlayerMenu> defaultMenu() {
    _playerMenuItems.sort((a, b) => a.id.compareTo(b.id));
    return _playerMenuItems;
  }

  List<PlayerMenu> menuByConfig() {
    return sortMenuItemsByIds(ids: Config().playerConfig.menuSort);
  }

  List<PlayerMenu> sortMenuItemsByIds({List<int> ids = const []}) {
    if (ids.isEmpty) {
      _playerMenuItems.sort((a, b) => a.id.compareTo(b.id));
      return _playerMenuItems;
    }

    return ids.map((id) {
      return _playerMenuItems.firstWhere((item) => item.id == id);
    }).toList();
  }
}
